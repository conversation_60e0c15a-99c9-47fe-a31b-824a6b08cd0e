{"autorun": false, "terminals": [{"name": "p01_0_0", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_1", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_2", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_3", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_4", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_5", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_6", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_7", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_8", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_9", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_10", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_11", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_12", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_13", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_14", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_15", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_16", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_17", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_18", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}, {"name": "p01_0_19", "description": "Run p01 example", "execute": true, "command": "sleep $((RANDOM % 10)); cargo run --release --example p01"}]}